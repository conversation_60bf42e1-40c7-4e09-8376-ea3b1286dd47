import { Chess } from 'chess.js';
import StockfishEngine from '../engine/stockfish';

class ChessGame {
  constructor() {
    this.chess = new Chess();
    this.engine = new StockfishEngine();
    this.gameMode = 'human-vs-engine'; // 'human-vs-human', 'human-vs-engine', 'engine-vs-engine'
    this.playerColor = 'white'; // 'white' or 'black'
    this.engineColor = 'black';
    this.isEngineThinking = false;
    this.gameHistory = [];
    this.gameCallbacks = {
      onMove: null,
      onGameOver: null,
      onEngineMove: null,
      onError: null
    };
  }

  async initialize() {
    try {
      await this.engine.startEngine();
      await this.engine.newGame();
      return true;
    } catch (error) {
      console.error('Failed to initialize chess game:', error);
      if (this.gameCallbacks.onError) {
        this.gameCallbacks.onError('Failed to initialize engine');
      }
      return false;
    }
  }

  // Set up event callbacks
  setCallbacks(callbacks) {
    this.gameCallbacks = { ...this.gameCallbacks, ...callbacks };
  }

  // Get current board state
  getBoard() {
    return this.chess.board();
  }

  // Get current FEN
  getFen() {
    return this.chess.fen();
  }

  // Get current PGN
  getPgn() {
    return this.chess.pgn();
  }

  // Get game status
  getGameStatus() {
    if (this.chess.isCheckmate()) {
      return {
        status: 'checkmate',
        winner: this.chess.turn() === 'w' ? 'black' : 'white'
      };
    } else if (this.chess.isDraw()) {
      return {
        status: 'draw',
        reason: this.getDrawReason()
      };
    } else if (this.chess.isCheck()) {
      return {
        status: 'check',
        turn: this.chess.turn()
      };
    } else {
      return {
        status: 'playing',
        turn: this.chess.turn()
      };
    }
  }

  getDrawReason() {
    if (this.chess.isStalemate()) return 'stalemate';
    if (this.chess.isThreefoldRepetition()) return 'threefold repetition';
    if (this.chess.isInsufficientMaterial()) return 'insufficient material';
    return 'fifty-move rule';
  }

  // Get legal moves for a square
  getLegalMoves(square) {
    return this.chess.moves({ square, verbose: true });
  }

  // Get all legal moves
  getAllLegalMoves() {
    return this.chess.moves({ verbose: true });
  }

  // Make a move
  async makeMove(from, to, promotion = null) {
    try {
      // Validate and make the move
      const moveOptions = { from, to };
      if (promotion) {
        moveOptions.promotion = promotion;
      }

      const move = this.chess.move(moveOptions);
      
      if (!move) {
        throw new Error('Invalid move');
      }

      // Add to history
      this.gameHistory.push({
        move,
        fen: this.chess.fen(),
        timestamp: new Date()
      });

      // Notify move callback
      if (this.gameCallbacks.onMove) {
        this.gameCallbacks.onMove(move, this.getGameStatus());
      }

      // Check if game is over
      const status = this.getGameStatus();
      if (status.status === 'checkmate' || status.status === 'draw') {
        if (this.gameCallbacks.onGameOver) {
          this.gameCallbacks.onGameOver(status);
        }
        return move;
      }

      // If it's engine's turn, make engine move
      if (this.gameMode === 'human-vs-engine' && 
          this.chess.turn() === this.engineColor.charAt(0)) {
        await this.makeEngineMove();
      }

      return move;
    } catch (error) {
      console.error('Error making move:', error);
      if (this.gameCallbacks.onError) {
        this.gameCallbacks.onError(error.message);
      }
      throw error;
    }
  }

  // Make engine move
  async makeEngineMove() {
    if (this.isEngineThinking) {
      return;
    }

    try {
      this.isEngineThinking = true;

      // Set current position
      await this.engine.setPosition(this.chess.fen());

      // Get best move from engine
      const bestMove = await this.engine.getBestMove(10); // depth 10

      if (!bestMove) {
        throw new Error('Engine failed to find a move');
      }

      // Parse the move (format: e2e4, e7e8q for promotion)
      const from = bestMove.substring(0, 2);
      const to = bestMove.substring(2, 4);
      const promotion = bestMove.length > 4 ? bestMove.substring(4) : null;

      // Make the engine move
      const move = this.chess.move({ from, to, promotion });

      if (!move) {
        throw new Error('Engine suggested invalid move: ' + bestMove);
      }

      // Add to history
      this.gameHistory.push({
        move,
        fen: this.chess.fen(),
        timestamp: new Date(),
        isEngineMove: true
      });

      // Notify callbacks
      if (this.gameCallbacks.onEngineMove) {
        this.gameCallbacks.onEngineMove(move, this.getGameStatus());
      }

      if (this.gameCallbacks.onMove) {
        this.gameCallbacks.onMove(move, this.getGameStatus());
      }

      // Check if game is over
      const status = this.getGameStatus();
      if (status.status === 'checkmate' || status.status === 'draw') {
        if (this.gameCallbacks.onGameOver) {
          this.gameCallbacks.onGameOver(status);
        }
      }

      return move;
    } catch (error) {
      console.error('Error making engine move:', error);
      if (this.gameCallbacks.onError) {
        this.gameCallbacks.onError('Engine error: ' + error.message);
      }
    } finally {
      this.isEngineThinking = false;
    }
  }

  // Start new game
  async newGame(playerColor = 'white') {
    this.chess.reset();
    this.playerColor = playerColor;
    this.engineColor = playerColor === 'white' ? 'black' : 'white';
    this.gameHistory = [];
    this.isEngineThinking = false;

    await this.engine.newGame();

    // If engine plays white, make first move
    if (this.engineColor === 'white') {
      await this.makeEngineMove();
    }
  }

  // Load game from FEN
  loadFen(fen) {
    try {
      this.chess.load(fen);
      this.gameHistory = [];
      return true;
    } catch (error) {
      console.error('Invalid FEN:', error);
      return false;
    }
  }

  // Load game from PGN
  loadPgn(pgn) {
    try {
      this.chess.loadPgn(pgn);
      this.gameHistory = [];
      return true;
    } catch (error) {
      console.error('Invalid PGN:', error);
      return false;
    }
  }

  // Undo last move
  undoMove() {
    const move = this.chess.undo();
    if (move) {
      this.gameHistory.pop();
      return move;
    }
    return null;
  }

  // Set engine difficulty (1-20)
  async setDifficulty(level) {
    await this.engine.setDifficulty(level);
  }

  // Get current turn
  getTurn() {
    return this.chess.turn() === 'w' ? 'white' : 'black';
  }

  // Check if it's player's turn
  isPlayerTurn() {
    return this.getTurn() === this.playerColor;
  }

  // Check if it's engine's turn
  isEngineTurn() {
    return this.getTurn() === this.engineColor;
  }

  // Get move history
  getHistory() {
    return this.gameHistory;
  }

  // Cleanup
  async cleanup() {
    await this.engine.stopEngine();
  }
}

export default ChessGame;
