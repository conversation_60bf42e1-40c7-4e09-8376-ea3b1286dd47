package com.chessapp;

import android.content.Context;
import android.content.res.AssetManager;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;

public class StockfishModule extends ReactContextBaseJavaModule {
    private static final String MODULE_NAME = "StockfishModule";
    private Process stockfishProcess;
    private OutputStreamWriter processInput;
    private BufferedReader processOutput;
    private String stockfishPath;

    public StockfishModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @ReactMethod
    public void initializeEngine(Promise promise) {
        try {
            // Copy stockfish binary from assets to internal storage
            Context context = getReactApplicationContext();
            File internalDir = context.getFilesDir();
            File stockfishFile = new File(internalDir, "stockfish");
            
            if (!stockfishFile.exists()) {
                AssetManager assetManager = context.getAssets();
                InputStream inputStream = assetManager.open("stockfish");
                FileOutputStream outputStream = new FileOutputStream(stockfishFile);
                
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
                
                inputStream.close();
                outputStream.close();
                
                // Make executable
                stockfishFile.setExecutable(true);
            }
            
            stockfishPath = stockfishFile.getAbsolutePath();
            
            // Start stockfish process
            ProcessBuilder processBuilder = new ProcessBuilder(stockfishPath);
            processBuilder.redirectErrorStream(true);
            stockfishProcess = processBuilder.start();
            
            processInput = new OutputStreamWriter(stockfishProcess.getOutputStream());
            processOutput = new BufferedReader(new InputStreamReader(stockfishProcess.getInputStream()));
            
            // Send UCI command and wait for uciok
            sendCommand("uci");
            String response = waitForResponse("uciok", 5000);
            
            if (response != null) {
                promise.resolve("Engine initialized successfully");
            } else {
                promise.reject("INIT_ERROR", "Failed to initialize engine");
            }
            
        } catch (Exception e) {
            promise.reject("INIT_ERROR", "Failed to initialize engine: " + e.getMessage());
        }
    }

    @ReactMethod
    public void sendCommand(String command, Promise promise) {
        try {
            if (processInput == null) {
                promise.reject("ENGINE_ERROR", "Engine not initialized");
                return;
            }
            
            processInput.write(command + "\n");
            processInput.flush();
            
            if (command.startsWith("go")) {
                // Wait for bestmove response
                String response = waitForResponse("bestmove", 30000);
                if (response != null) {
                    WritableMap result = Arguments.createMap();
                    result.putString("response", response);
                    promise.resolve(result);
                } else {
                    promise.reject("TIMEOUT", "Engine response timeout");
                }
            } else {
                promise.resolve("Command sent");
            }
            
        } catch (Exception e) {
            promise.reject("COMMAND_ERROR", "Failed to send command: " + e.getMessage());
        }
    }

    @ReactMethod
    public void sendCommandSync(String command) {
        try {
            if (processInput != null) {
                processInput.write(command + "\n");
                processInput.flush();
            }
        } catch (Exception e) {
            // Silent fail for sync commands
        }
    }

    @ReactMethod
    public void stopEngine(Promise promise) {
        try {
            if (stockfishProcess != null) {
                sendCommandSync("quit");
                stockfishProcess.destroy();
                stockfishProcess = null;
                processInput = null;
                processOutput = null;
            }
            promise.resolve("Engine stopped");
        } catch (Exception e) {
            promise.reject("STOP_ERROR", "Failed to stop engine: " + e.getMessage());
        }
    }

    private void sendCommand(String command) throws IOException {
        if (processInput != null) {
            processInput.write(command + "\n");
            processInput.flush();
        }
    }

    private String waitForResponse(String expectedStart, int timeoutMs) {
        try {
            long startTime = System.currentTimeMillis();
            StringBuilder response = new StringBuilder();
            
            while (System.currentTimeMillis() - startTime < timeoutMs) {
                if (processOutput.ready()) {
                    String line = processOutput.readLine();
                    if (line != null) {
                        response.append(line).append("\n");
                        if (line.startsWith(expectedStart)) {
                            return response.toString();
                        }
                    }
                }
                Thread.sleep(10);
            }
        } catch (Exception e) {
            // Return null on error
        }
        return null;
    }

    @Override
    public void onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy();
        if (stockfishProcess != null) {
            stockfishProcess.destroy();
        }
    }
}
