/**
 * Chess App with Stockfish Engine
 * React Native Chess Application
 *
 * @format
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  useColorScheme,
} from 'react-native';
import { Chessboard } from 'react-native-chessboard';
import ChessGame from './game/ChessGame';

function App() {
  const isDarkMode = useColorScheme() === 'dark';
  const [game, setGame] = useState<ChessGame | null>(null);
  const [boardPosition, setBoardPosition] = useState('rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1');
  const [gameStatus, setGameStatus] = useState({ status: 'playing', turn: 'white' });
  const [isEngineThinking, setIsEngineThinking] = useState(false);
  const [playerColor, setPlayerColor] = useState<'white' | 'black'>('white');
  const [gameHistory, setGameHistory] = useState<any[]>([]);

  // Initialize game
  useEffect(() => {
    const initializeGame = async () => {
      const chessGame = new ChessGame();

      // Set up callbacks
      chessGame.setCallbacks({
        onMove: (move, status) => {
          setBoardPosition(chessGame.getFen());
          setGameStatus(status);
          setGameHistory([...chessGame.getHistory()]);
        },
        onEngineMove: (move, status) => {
          setIsEngineThinking(false);
          setBoardPosition(chessGame.getFen());
          setGameStatus(status);
          setGameHistory([...chessGame.getHistory()]);
        },
        onGameOver: (status) => {
          let message = '';
          if (status.status === 'checkmate') {
            message = `Checkmate! ${status.winner} wins!`;
          } else if (status.status === 'draw') {
            message = `Game drawn by ${status.reason}`;
          }
          Alert.alert('Game Over', message);
        },
        onError: (error) => {
          Alert.alert('Error', error);
        }
      });

      const initialized = await chessGame.initialize();
      if (initialized) {
        setGame(chessGame);
        setBoardPosition(chessGame.getFen());
        setGameStatus(chessGame.getGameStatus());
      } else {
        Alert.alert('Error', 'Failed to initialize chess engine');
      }
    };

    initializeGame();

    return () => {
      if (game) {
        game.cleanup();
      }
    };
  }, []);

  // Handle piece move
  const onMove = useCallback(async (from: string, to: string) => {
    if (!game || !game.isPlayerTurn() || isEngineThinking) {
      return false;
    }

    try {
      setIsEngineThinking(true);
      await game.makeMove(from, to);
      return true;
    } catch (error) {
      setIsEngineThinking(false);
      return false;
    }
  }, [game, isEngineThinking]);

  // Start new game
  const startNewGame = useCallback(async () => {
    if (!game) return;

    Alert.alert(
      'New Game',
      'Choose your color:',
      [
        {
          text: 'Play as White',
          onPress: async () => {
            setPlayerColor('white');
            await game.newGame('white');
            setBoardPosition(game.getFen());
            setGameStatus(game.getGameStatus());
            setGameHistory([]);
            setIsEngineThinking(false);
          }
        },
        {
          text: 'Play as Black',
          onPress: async () => {
            setPlayerColor('black');
            setIsEngineThinking(true);
            await game.newGame('black');
            setBoardPosition(game.getFen());
            setGameStatus(game.getGameStatus());
            setGameHistory([]);
          }
        },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  }, [game]);

  // Undo move
  const undoMove = useCallback(() => {
    if (!game || isEngineThinking) return;

    const undoneMove = game.undoMove();
    if (undoneMove) {
      // Undo engine move too if it was the last move
      if (game.getHistory().length > 0 &&
          game.getHistory()[game.getHistory().length - 1].isEngineMove) {
        game.undoMove();
      }

      setBoardPosition(game.getFen());
      setGameStatus(game.getGameStatus());
      setGameHistory([...game.getHistory()]);
    }
  }, [game, isEngineThinking]);

  const getStatusText = () => {
    if (isEngineThinking) {
      return 'Engine is thinking...';
    }

    if (gameStatus.status === 'checkmate') {
      return `Checkmate! ${gameStatus.winner} wins!`;
    } else if (gameStatus.status === 'draw') {
      return `Draw by ${gameStatus.reason}`;
    } else if (gameStatus.status === 'check') {
      return `${gameStatus.turn === 'w' ? 'White' : 'Black'} is in check`;
    } else {
      return `${gameStatus.turn === 'w' ? 'White' : 'Black'} to move`;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDarkMode ? '#000' : '#fff' }]}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDarkMode ? '#fff' : '#000' }]}>
          Chess vs Stockfish
        </Text>
        <Text style={[styles.status, { color: isDarkMode ? '#ccc' : '#666' }]}>
          {getStatusText()}
        </Text>
      </View>

      {/* Chessboard */}
      <View style={styles.boardContainer}>
        <Chessboard
          fen={boardPosition}
          onMove={onMove}
          boardOrientation={playerColor}
          animationDuration={300}
          boardSize={350}
          showCoordinates={true}
          lightSquareColor="#f0d9b5"
          darkSquareColor="#b58863"
        />
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.button, styles.newGameButton]}
          onPress={startNewGame}
          disabled={isEngineThinking}
        >
          <Text style={styles.buttonText}>New Game</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.undoButton]}
          onPress={undoMove}
          disabled={isEngineThinking || gameHistory.length === 0}
        >
          <Text style={styles.buttonText}>Undo</Text>
        </TouchableOpacity>
      </View>

      {/* Game Info */}
      <View style={styles.gameInfo}>
        <Text style={[styles.infoText, { color: isDarkMode ? '#ccc' : '#666' }]}>
          Playing as: {playerColor}
        </Text>
        <Text style={[styles.infoText, { color: isDarkMode ? '#ccc' : '#666' }]}>
          Moves: {Math.floor(gameHistory.length / 2) + 1}
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  status: {
    fontSize: 16,
  },
  boardContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  newGameButton: {
    backgroundColor: '#4CAF50',
  },
  undoButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  gameInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 40,
    paddingBottom: 20,
  },
  infoText: {
    fontSize: 14,
  },
});

export default App;
