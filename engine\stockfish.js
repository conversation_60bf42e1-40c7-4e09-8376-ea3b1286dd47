import { Platform, NativeModules } from 'react-native';

const { StockfishModule } = NativeModules;

class StockfishEngine {
  constructor() {
    this.isReady = false;
    this.outputCallback = null;
    this.useNativeModule = Platform.OS === 'android' && StockfishModule;
  }

  async startEngine() {
    try {
      if (this.useNativeModule) {
        // Use native module for real Stockfish integration
        await StockfishModule.initializeEngine();
        this.isReady = true;
        return true;
      } else {
        // Fallback to mock implementation
        this.isReady = true;
        return true;
      }
    } catch (error) {
      console.error('Failed to start Stockfish engine:', error);
      // Fallback to mock implementation
      this.isReady = true;
      return true;
    }
  }

  async sendCommand(command) {
    try {
      console.log('Sending command to Stockfish:', command);

      if (this.useNativeModule) {
        // Use native module for real Stockfish communication
        if (command.startsWith('go')) {
          const result = await StockfishModule.sendCommand(command);
          const response = result.response;
          if (this.outputCallback) {
            this.outputCallback(response);
          }
          return response;
        } else {
          // For non-go commands, send synchronously
          StockfishModule.sendCommandSync(command);
          return '';
        }
      } else {
        // Fallback mock implementation
        return this.mockSendCommand(command);
      }
    } catch (error) {
      console.error('Error sending command:', error);
      // Fallback to mock on error
      return this.mockSendCommand(command);
    }
  }

  async mockSendCommand(command) {
    // Simulate UCI responses for development/testing
    if (command === 'uci') {
      this.isReady = true;
      const response = `id name Stockfish 17.1
id author the Stockfish developers (see AUTHORS file)
uciok`;
      if (this.outputCallback) {
        this.outputCallback(response);
      }
      return response;
    }

    if (command === 'isready') {
      const response = 'readyok';
      if (this.outputCallback) {
        this.outputCallback(response);
      }
      return response;
    }

    if (command.startsWith('position')) {
      return '';
    }

    if (command.startsWith('go')) {
      // Simulate thinking time
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Return a random legal move for development
      const moves = ['e2e4', 'e7e5', 'd2d4', 'd7d5', 'g1f3', 'b8c6', 'f1c4', 'f8c5'];
      const randomMove = moves[Math.floor(Math.random() * moves.length)];
      const response = `info depth 10 score cp 25 nodes 12345 time 1000 pv ${randomMove}
bestmove ${randomMove}`;

      if (this.outputCallback) {
        this.outputCallback(response);
      }
      return response;
    }

    return '';
  }

  onOutput(callback) {
    this.outputCallback = callback;
  }

  async setPosition(fen) {
    await this.sendCommand(`position fen ${fen}`);
  }

  async setStartPosition() {
    await this.sendCommand('position startpos');
  }

  async setPositionWithMoves(moves) {
    const movesStr = moves.join(' ');
    await this.sendCommand(`position startpos moves ${movesStr}`);
  }

  async getBestMove(depth = 10, timeMs = null) {
    let goCommand = 'go';

    if (timeMs) {
      goCommand += ` movetime ${timeMs}`;
    } else {
      goCommand += ` depth ${depth}`;
    }

    const response = await this.sendCommand(goCommand);

    // Parse bestmove from response
    const match = response.match(/bestmove (\w+)/);
    return match ? match[1] : null;
  }

  async stopEngine() {
    this.isReady = false;
    if (this.useNativeModule) {
      try {
        await StockfishModule.stopEngine();
      } catch (error) {
        console.error('Error stopping engine:', error);
      }
    }
  }

  async setOption(name, value) {
    await this.sendCommand(`setoption name ${name} value ${value}`);
  }

  async newGame() {
    await this.sendCommand('ucinewgame');
    await this.sendCommand('isready');
  }

  // Utility method to set difficulty level
  async setDifficulty(level) {
    // Level 1-20, where 20 is strongest
    await this.setOption('Skill Level', level);
  }

  // Utility method to set thinking time
  async setThinkingTime(timeMs) {
    this.thinkingTime = timeMs;
  }
}

export default StockfishEngine;
